import { useMemo, useState } from 'react';
import { MiniApp } from '@the-agent/shared';
import { Dropdown, Tooltip, Modal, Input } from 'antd';
import { X, Plus, Search } from 'lucide-react';

import { useLiveQuery } from 'dexie-react-hooks';
import { db } from '~/storages/indexdb';
import MiniappEmptyState from '~/sidepanel/components/miniapp/MiniappEmptyState';
import MiniappApplicationsList from '~/sidepanel/components/miniapp/MiniappApplicationsList';
import newMiniappImg from '~/assets/imgs/new-miniapp.png';
import { useNavigate } from 'react-router-dom';
import { useUser } from '~/hooks/useUser';
import { createNewMiniApp } from '~/services/conversation';
import {
  deleteMiniapp as deleteMiniappService,
  updateMiniapp as updateMiniappService,
} from '~/services/miniapp';
import { useLanguage } from '~/utils/i18n';
import newminiappIcon from '~/assets/icons/newminiapp.svg';
import archiveIcon from '~/assets/icons/archive.svg';

type FilterType = 'All' | 'Installed' | 'Uninstalled';
type TabType = 'script' | 'workflow';

export const MiniappsPage = () => {
  const navigate = useNavigate();
  const { getMessage } = useLanguage();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [projectName, setProjectName] = useState('');
  const [pluginType, setPluginType] = useState<'script' | 'workflow'>('script');
  const [showArchivedView, setShowArchivedView] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<number | null>(null);
  const activeUser = useUser();
  const [filter, setFilter] = useState<FilterType>('All');
  const [activeTab, setActiveTab] = useState<TabType>('script');

  // Library selection states
  const [showLibraryDropdown, setShowLibraryDropdown] = useState(false);
  const [selectedLibraries, setSelectedLibraries] = useState<string[]>([]);
  const [librarySearchTerm, setLibrarySearchTerm] = useState('');

  // Mock data for library categories and nodes
  const libraryCategories = useMemo(
    () => [
      {
        name: 'General',
        nodes: ['ClickNode', 'InputNode', 'SendKeys'],
      },
      {
        name: 'Human Resources',
        nodes: ['EmployeeNode', 'PayrollNode', 'AttendanceNode'],
      },
      {
        name: 'Research & Development',
        nodes: ['DataAnalysisNode', 'ExperimentNode', 'ReportNode'],
      },
      {
        name: 'Finance',
        nodes: ['BudgetNode', 'InvoiceNode', 'PaymentNode'],
      },
      {
        name: 'IT',
        nodes: ['ServerNode', 'DatabaseNode', 'SecurityNode'],
      },
      {
        name: 'Sales',
        nodes: ['LeadNode', 'ContactNode', 'DealNode'],
      },
      {
        name: 'After Sales',
        nodes: ['SupportNode', 'FeedbackNode', 'MaintenanceNode'],
      },
    ],
    []
  );

  const miniapps = useLiveQuery(async () => {
    if (!activeUser?.id) return [];
    if (showArchivedView) {
      const archivedApps = await db.getArchivedApps();
      return archivedApps;
    }
    const allApps = await db.getAllApps('All');
    return allApps;
  }, [activeUser?.id, showArchivedView]);

  // Filter miniapps by tab and filter
  const filteredMiniapps = useMemo(() => {
    if (!miniapps) return [];

    // First filter by active tab (type)
    const tabFilteredApps = miniapps.filter(app => app.type === activeTab);

    // Then apply the installation filter
    if (filter === 'All') return tabFilteredApps;
    if (filter === 'Installed')
      return tabFilteredApps.filter(app => app.installation && app.installation.code);
    if (filter === 'Uninstalled')
      return tabFilteredApps.filter(app => !app.installation || !app.installation.code);
    return tabFilteredApps;
  }, [miniapps, filter, activeTab]);

  // Filter dropdown items
  const filterItems = [
    { key: 'All', label: getMessage('filterAll') },
    { key: 'Installed', label: getMessage('filterInstalled') },
    { key: 'Uninstalled', label: getMessage('filterUninstalled') },
  ];

  const filterLabel = useMemo(() => {
    switch (filter) {
      case 'All':
        return getMessage('filterAll');
      case 'Installed':
        return getMessage('filterInstalled');
      case 'Uninstalled':
        return getMessage('filterUninstalled');
      default:
        return filter;
    }
  }, [filter, getMessage]);

  const handleNewProject = () => {
    setShowCreateModal(true);
  };

  const handleCreateProject = async () => {
    if (!projectName.trim() || !activeUser?.id) return;

    try {
      const miniapp = await createNewMiniApp(activeUser.id, projectName.trim(), pluginType);
      navigate(`/miniapp/${miniapp.id}?pluginType=${pluginType}`);

      setShowCreateModal(false);
      setProjectName('');
      setPluginType('script');
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleCancelCreate = () => {
    setShowCreateModal(false);
    setProjectName('');
    setPluginType('script');
    setSelectedLibraries([]);
    setShowLibraryDropdown(false);
    setLibrarySearchTerm('');
  };

  // Library management functions
  const handleAddLibrary = () => {
    setShowLibraryDropdown(true);
  };

  const handleLibrarySelect = (nodeId: string) => {
    if (!selectedLibraries.includes(nodeId)) {
      setSelectedLibraries([...selectedLibraries, nodeId]);
    }
  };

  const handleRemoveLibrary = (nodeId: string) => {
    setSelectedLibraries(selectedLibraries.filter(id => id !== nodeId));
  };

  // Filter categories and nodes based on search term
  const filteredCategories = useMemo(() => {
    if (!librarySearchTerm) return libraryCategories;

    return libraryCategories
      .map(category => ({
        ...category,
        nodes: category.nodes.filter(
          node =>
            node.toLowerCase().includes(librarySearchTerm.toLowerCase()) ||
            category.name.toLowerCase().includes(librarySearchTerm.toLowerCase())
        ),
      }))
      .filter(category => category.nodes.length > 0);
  }, [libraryCategories, librarySearchTerm]);

  const handleSelectMiniapp = (miniapp: MiniApp) => {
    navigate(`/miniapp/${miniapp.id}?pluginType=${miniapp.type}`);
  };

  const handleArchiveMiniapp = async (miniapp: MiniApp) => {
    try {
      await updateMiniappService(miniapp.id, { status: 'archived' });
    } catch (error) {
      console.error('Failed to archive miniapp:', error);
    }
  };

  const handleActivateMiniapp = async (miniapp: MiniApp) => {
    try {
      await updateMiniappService(miniapp.id, { status: 'active' });
    } catch (error) {
      console.error('Failed to activate miniapp:', error);
    }
  };

  const handleDeleteMiniapp = (miniapp: MiniApp) => {
    setConfirmDelete(miniapp.id);
  };

  const handleCancelDelete = () => setConfirmDelete(null);

  const handleConfirmDelete = async () => {
    if (confirmDelete == null) return;
    try {
      await deleteMiniappService(confirmDelete);
    } catch (error) {
      console.error('Failed to delete miniapp:', error);
    } finally {
      setConfirmDelete(null);
    }
  };

  const handleShowArchived = () => {
    setShowArchivedView(true);
  };

  return (
    <div style={{ position: 'fixed', inset: 0, zIndex: 50 }}>
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundColor: '#ffffff',
          boxShadow: '0 0 15px 0 rgba(0, 0, 0, 0.15)',
          overflow: 'hidden',
          zIndex: 10,
        }}
      >
        {/* Header */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 16px',
            height: '44px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
            {showArchivedView ? getMessage('archived') : getMessage('miniapp')}
          </h2>
          <Tooltip title={getMessage('tooltipClose')} placement="bottom">
            <button
              onClick={() =>
                showArchivedView ? setShowArchivedView(false) : navigate('/conversation')
              }
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>

        {/* Content */}
        <div
          style={{
            flex: 1,
            height: 'calc(100vh - 44px)',
          }}
        >
          {/* Tab switcher */}
          <div
            style={{
              display: 'flex',
              padding: '16px 16px 0 16px',
              backgroundColor: '#ffffff',
            }}
          >
            <div
              style={{
                display: 'flex',
                backgroundColor: '#f3f4f6',
                borderRadius: '8px',
                padding: '4px',
                gap: '4px',
                width: '100%',
                justifyContent: 'space-between',
              }}
            >
              <button
                onClick={() => setActiveTab('script')}
                style={{
                  width: '50%',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  border: 'none',
                  backgroundColor: activeTab === 'script' ? '#ffffff' : 'transparent',
                  color: activeTab === 'script' ? '#111827' : '#6b7280',
                  fontSize: '14px',
                  fontWeight: 500,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  boxShadow: activeTab === 'script' ? '0 1px 2px 0 rgba(0, 0, 0, 0.05)' : 'none',
                }}
              >
                {getMessage('pluginTypeScript')}
              </button>
              <button
                onClick={() => setActiveTab('workflow')}
                style={{
                  width: '50%',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  border: 'none',
                  backgroundColor: activeTab === 'workflow' ? '#ffffff' : 'transparent',
                  color: activeTab === 'workflow' ? '#111827' : '#6b7280',
                  fontSize: '14px',
                  fontWeight: 500,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  boxShadow: activeTab === 'workflow' ? '0 1px 2px 0 rgba(0, 0, 0, 0.05)' : 'none',
                }}
              >
                {getMessage('pluginTypeWorkflow')}
              </button>
            </div>
          </div>
          {/* Pin top bar */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '12px 16px 0px 16px',
              backgroundColor: '#ffffff',
            }}
          >
            {/* Filter dropdown */}
            <Dropdown
              menu={{
                items: filterItems,
                onClick: ({ key }) => setFilter(key as FilterType),
              }}
              trigger={['click']}
            >
              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '8px 12px',
                  borderRadius: '6px',
                  border: '0px solid #d1d5db',
                  backgroundColor: '#ffffff',
                  color: '#374151',
                  fontSize: '14px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                }}
              >
                {filterLabel}
                <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                  <path
                    d="M3 4.5L6 7.5L9 4.5"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    fill="none"
                  />
                </svg>
              </button>
            </Dropdown>

            {/* Archived button - only show in non-archived view */}
            <div style={{ display: 'flex', gap: '12px' }}>
              <Tooltip title={getMessage('newProject')}>
                <button
                  onClick={handleNewProject}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '32px',
                    height: '32px',
                    borderRadius: '6px',
                    border: 'none',
                    backgroundColor: 'transparent',
                    cursor: 'pointer',
                    transition: 'background 0.2s',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <img src={newminiappIcon} alt="New Project" style={{ width: 18, height: 18 }} />
                </button>
              </Tooltip>
              <Tooltip title={getMessage('archived')}>
                <button
                  onClick={handleShowArchived}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '32px',
                    height: '32px',
                    borderRadius: '6px',
                    border: 'none',
                    backgroundColor: 'transparent',
                    cursor: 'pointer',
                    transition: 'background 0.2s',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <img src={archiveIcon} alt="Archive" style={{ width: 18, height: 18 }} />
                </button>
              </Tooltip>
            </div>
          </div>
          {showArchivedView ? (
            // Archived view
            filteredMiniapps && filteredMiniapps.length > 0 ? (
              <MiniappApplicationsList
                miniapps={filteredMiniapps}
                onSelectMiniapp={handleSelectMiniapp}
                onNewProject={handleNewProject}
                onArchiveMiniapp={handleArchiveMiniapp}
                onDeleteMiniapp={handleDeleteMiniapp}
                onActivateMiniapp={handleActivateMiniapp}
                isArchivedView={true}
              />
            ) : (
              <MiniappEmptyState fromArchived={true} />
            )
          ) : // Main view
          filteredMiniapps && filteredMiniapps.length > 0 ? (
            <MiniappApplicationsList
              miniapps={filteredMiniapps}
              onSelectMiniapp={handleSelectMiniapp}
              onNewProject={handleNewProject}
              onArchiveMiniapp={handleArchiveMiniapp}
              onDeleteMiniapp={handleDeleteMiniapp}
              onActivateMiniapp={handleActivateMiniapp}
              onShowArchived={handleShowArchived}
              isArchivedView={false}
            />
          ) : (
            <MiniappEmptyState fromArchived={false} />
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <Modal
        open={confirmDelete != null}
        onCancel={handleCancelDelete}
        footer={
          <div style={{ display: 'flex', justifyContent: 'center', gap: 16, marginTop: 8 }}>
            <button
              onClick={handleCancelDelete}
              style={{
                fontWeight: 500,
                fontSize: 15,
                padding: '9px 22px',
                borderRadius: 7,
                border: '1px solid #D1D5DB',
                color: '#111827',
                background: '#fff',
                cursor: 'pointer',
                transition: 'background 0.2s, border 0.2s',
              }}
            >
              {getMessage('cancel')}
            </button>
            <button
              onClick={handleConfirmDelete}
              style={{
                fontWeight: 600,
                fontSize: 15,
                padding: '9px 22px',
                borderRadius: 7,
                border: 'none',
                background: '#DC2626',
                color: '#fff',
                cursor: 'pointer',
                boxShadow: '0 2px 8px 0 rgba(220,38,38,0.08)',
                transition: 'background 0.2s',
              }}
            >
              {getMessage('delete')}
            </button>
          </div>
        }
        centered
        closable={false}
        width={300}
        styles={{
          mask: { background: 'rgba(0,0,0,0.18)' },
          content: { borderRadius: 24 },
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <h3 style={{ fontSize: 22, fontWeight: 700, marginBottom: 18 }}>
            {getMessage('deleteMiniappTitle')}
          </h3>
          <div style={{ fontSize: 17, color: '#374151', marginBottom: 28 }}>
            {getMessage('deleteMiniappContent')}
          </div>
        </div>
      </Modal>

      {/* Create Project Modal */}
      <Modal
        open={showCreateModal}
        onCancel={handleCancelCreate}
        footer={null}
        centered
        width={300}
        closable={false}
      >
        <div style={{ textAlign: 'center' }}>
          <img
            src={newMiniappImg}
            alt="Create Project"
            style={{
              width: '80px',
              height: '80px',
              objectFit: 'contain',
            }}
          />

          {/* Title */}
          <h2
            style={{
              fontSize: '16px',
              fontWeight: 600,
              color: '#111827',
              marginBottom: '24px',
              margin: '0 0 24px 0',
            }}
          >
            {getMessage('createProjectTitle')}
          </h2>

          {/* Form */}
          <div style={{ textAlign: 'left', marginBottom: '24px' }}>
            {/* Plugin type selection */}
            <label
              style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: 500,
                color: '#374151',
                marginBottom: '12px',
              }}
            >
              {getMessage('pluginType')}
            </label>
            <div style={{ display: 'flex', gap: '16px', marginBottom: '20px' }}>
              <label
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#374151',
                }}
              >
                <input
                  type="radio"
                  name="pluginType"
                  value="script"
                  checked={pluginType === 'script'}
                  onChange={e => setPluginType(e.target.value as 'script' | 'workflow')}
                  style={{
                    width: '16px',
                    height: '16px',
                    accentColor: '#000',
                    margin: 0,
                  }}
                />
                {getMessage('pluginTypeScript')}
              </label>
              <label
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#374151',
                }}
              >
                <input
                  type="radio"
                  name="pluginType"
                  value="workflow"
                  checked={pluginType === 'workflow'}
                  onChange={e => setPluginType(e.target.value as 'script' | 'workflow')}
                  style={{
                    width: '16px',
                    height: '16px',
                    accentColor: '#000',
                    margin: 0,
                  }}
                />
                {getMessage('pluginTypeWorkflow')}
              </label>
            </div>

            {/* Library selection for workflow type */}
            {pluginType === 'workflow' && (
              <div style={{ marginBottom: '20px' }}>
                {/* Add Library Button */}
                <button
                  onClick={handleAddLibrary}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    width: '100%',
                    padding: '10px 14px',
                    borderRadius: '8px',
                    border: '2px dashed #d1d5db',
                    backgroundColor: 'transparent',
                    color: '#6b7280',
                    fontSize: '14px',
                    fontWeight: 500,
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    marginBottom: '12px',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.borderColor = '#9ca3af';
                    e.currentTarget.style.color = '#374151';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.color = '#6b7280';
                  }}
                >
                  <Plus size={16} />
                  添加 智能体
                </button>

                {/* Selected Libraries Display */}
                {selectedLibraries.length > 0 && (
                  <div style={{ marginBottom: '12px' }}>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                      {selectedLibraries.map(nodeId => (
                        <div
                          key={nodeId}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '6px',
                            padding: '4px 8px',
                            backgroundColor: '#f3f4f6',
                            borderRadius: '6px',
                            fontSize: '12px',
                            color: '#374151',
                          }}
                        >
                          {nodeId}
                          <button
                            onClick={() => handleRemoveLibrary(nodeId)}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: '16px',
                              height: '16px',
                              borderRadius: '50%',
                              border: 'none',
                              backgroundColor: '#d1d5db',
                              color: '#6b7280',
                              cursor: 'pointer',
                              fontSize: '10px',
                            }}
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Multi-select Dropdown */}
                {showLibraryDropdown && (
                  <div
                    style={{
                      position: 'relative',
                      backgroundColor: '#ffffff',
                      borderRadius: '12px',
                      border: '1px solid #e5e7eb',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      maxHeight: '200px',
                      overflow: 'hidden',
                    }}
                  >
                    {/* Fixed Search Input */}
                    <div
                      style={{
                        position: 'sticky',
                        top: 0,
                        zIndex: 10,
                        backgroundColor: '#ffffff',
                        padding: '16px',
                        borderBottom: '1px solid #f3f4f6',
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          padding: '8px 12px',
                          backgroundColor: '#f9fafb',
                          borderRadius: '8px',
                          border: '1px solid #e5e7eb',
                        }}
                      >
                        <Search size={16} color="#6b7280" />
                        <input
                          type="text"
                          placeholder="搜索类别..."
                          value={librarySearchTerm}
                          onChange={e => setLibrarySearchTerm(e.target.value)}
                          style={{
                            flex: 1,
                            border: 'none',
                            backgroundColor: 'transparent',
                            color: '#374151',
                            fontSize: '14px',
                            outline: 'none',
                          }}
                        />
                      </div>
                    </div>

                    {/* Scrollable Categories List */}
                    <div
                      style={{
                        padding: '16px',
                        maxHeight: '200px',
                        overflowY: 'auto',
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '12px',
                      }}
                    >
                      {filteredCategories.map(category => (
                        <div key={category.name}>
                          <h3
                            style={{
                              color: '#111827',
                              fontSize: '16px',
                              fontWeight: 600,
                              marginBottom: '8px',
                            }}
                          >
                            {category.name}
                          </h3>
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                            {category.nodes.map(node => (
                              <button
                                key={node}
                                onClick={() => handleLibrarySelect(node)}
                                disabled={selectedLibraries.includes(node)}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  padding: '8px 12px',
                                  backgroundColor: selectedLibraries.includes(node)
                                    ? '#f3f4f6'
                                    : 'transparent',
                                  color: selectedLibraries.includes(node) ? '#9ca3af' : '#374151',
                                  border: 'none',
                                  borderRadius: '6px',
                                  fontSize: '14px',
                                  cursor: selectedLibraries.includes(node)
                                    ? 'not-allowed'
                                    : 'pointer',
                                  transition: 'all 0.2s',
                                  textAlign: 'left',
                                }}
                                onMouseOver={e => {
                                  if (!selectedLibraries.includes(node)) {
                                    e.currentTarget.style.backgroundColor = '#f9fafb';
                                  }
                                }}
                                onMouseOut={e => {
                                  if (!selectedLibraries.includes(node)) {
                                    e.currentTarget.style.backgroundColor = 'transparent';
                                  }
                                }}
                              >
                                {node}
                              </button>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Fixed Close Button */}
                    <button
                      onClick={() => setShowLibraryDropdown(false)}
                      style={{
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        border: 'none',
                        backgroundColor: '#f3f4f6',
                        color: '#6b7280',
                        cursor: 'pointer',
                        fontSize: '16px',
                        zIndex: 20,
                      }}
                      onMouseOver={e => {
                        e.currentTarget.style.backgroundColor = '#e5e7eb';
                      }}
                      onMouseOut={e => {
                        e.currentTarget.style.backgroundColor = '#f3f4f6';
                      }}
                    >
                      ×
                    </button>
                  </div>
                )}
              </div>
            )}

            <label
              style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: 500,
                color: '#374151',
                marginBottom: '6px',
              }}
            >
              {getMessage('projectNameLabel')}
            </label>
            <Input
              placeholder={getMessage('pleaseEnterPlaceholder')}
              value={projectName}
              onChange={e => setProjectName(e.target.value)}
              onPressEnter={handleCreateProject}
              style={{
                height: '40px',
                fontSize: '14px',
                borderRadius: '6px',
              }}
              autoFocus
            />
          </div>

          {/* Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <button
              onClick={handleCreateProject}
              disabled={!projectName.trim()}
              style={{
                width: '100%',
                height: '40px',
                borderRadius: '6px',
                border: 'none',
                backgroundColor: projectName.trim() ? '#000' : '#d1d5db',
                color: '#ffffff',
                fontSize: '14px',
                fontWeight: 500,
                cursor: projectName.trim() ? 'pointer' : 'not-allowed',
                transition: 'background-color 0.2s',
              }}
            >
              {getMessage('create')}
            </button>
            <button
              onClick={handleCancelCreate}
              style={{
                width: '100%',
                height: '40px',
                borderRadius: '6px',
                border: '1px solid #d1d5db',
                backgroundColor: '#ffffff',
                color: '#374151',
                fontSize: '14px',
                fontWeight: 500,
                cursor: 'pointer',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#ffffff';
              }}
            >
              {getMessage('cancel')}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
